# ------------ Base (tools shared) ------------
FROM node:20-alpine AS base
ENV PNPM_HOME=/root/.local/share/pnpm \
    NPM_CONFIG_UPDATE_NOTIFIER=false \
    NUXT_TELEMETRY_DISABLED=1
ENV PATH=$PNPM_HOME:$PATH
# Dùng corepack để pin pnpm, ổn định hơn npm -g
RUN corepack enable && corepack prepare pnpm@9.15.1 --activate

# ------------ Build stage ------------
FROM base AS builder
WORKDIR /app

# 1) Chỉ copy file manifest để tối đa cache
#    NHỚ: nên có pnpm-lock.yaml trong repo
COPY package.json pnpm-lock.yaml* ./

# 2) Dựng cache phụ thuộc trước khi copy source
#    fetch + install offline giúp giảm truy cập mạng khi build
#    Increase heap size for build process
ENV NODE_OPTIONS="--max-old-space-size=1024"
RUN --mount=type=cache,id=pnpm-store,target=/root/.local/share/pnpm/store \
    pnpm fetch

# 3) Copy source code
COPY . .

# 4) Cài deps từ cache (offline) và build
RUN --mount=type=cache,id=pnpm-store,target=/root/.local/share/pnpm/store \
    pnpm install --offline --frozen-lockfile
# Tuỳ dự án, preset node-server cho Nitro thường hợp lý
ENV NITRO_PRESET=node-server
# Ensure sufficient memory for build process
ENV NODE_OPTIONS="--max-old-space-size=1024"
RUN pnpm build

# Optional: dọn bớt để giảm kích thước layer build (không ảnh hưởng runtime)
RUN rm -rf node_modules .pnpm-store .nuxt

# ------------ Runtime (production) ------------
FROM node:20-alpine AS production
WORKDIR /app

# copy output của Nuxt; runtime thường không cần node_modules
COPY --from=builder /app/.output ./.output

# (Tuỳ chọn) nếu bạn cần metadata/app version
COPY package.json ./

COPY .env* ./

# Tạo user không phải root
RUN addgroup -g 1001 -S nodejs && adduser -S nuxt -u 1001 -G nodejs
RUN chown -R nuxt:nodejs /app
USER nuxt

# Port app
EXPOSE 3005

# Env cơ bản
ENV NODE_ENV=production \
    HOST=0.0.0.0 \
    PORT=3005

# Start
CMD ["node", ".output/server/index.mjs"]
