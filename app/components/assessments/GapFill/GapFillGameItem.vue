<script setup lang="ts">
import type { AssessmentGapFill } from '@/types/course'
import { CheckCircle, ChevronRight, Flag, XCircle } from 'lucide-vue-next'

interface Props {
  assessment: AssessmentGapFill
  isLastQuestion: boolean
}

interface Emits {
  answerResult: [result: { isCorrect: boolean, score: number }]
  nextQuestion: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Internal component state
const userAnswers = ref<string[]>([])
const answerSubmitted = ref(false)
const showFeedback = ref(false)
const isCorrect = ref(false)
const loading = ref(false)
const validationError = ref<string | null>(null)

// Parse question for gaps
const questionParts = computed(() => {
  const parts: { text: string, isGap: boolean }[] = []
  const question = props.assessment.question

  // Split by gaps (multiple underscores)
  const segments = question.split(/_{3,}/)

  for (let i = 0; i < segments.length; i++) {
    if (segments[i]) {
      parts.push({ text: segments[i], isGap: false })
    }

    // Add gap after each segment except the last
    if (i < segments.length - 1) {
      parts.push({ text: '___', isGap: true })
    }
  }

  return parts
})

// Get gap positions for input management
const gapPositions = computed(() => {
  const gaps: number[] = []
  questionParts.value.forEach((part, index) => {
    if (part.isGap) {
      gaps.push(index)
    }
  })
  return gaps
})

// Get gap index from part index
function getGapIndex(partIndex: number): number {
  let gapCount = 0
  for (let i = 0; i < partIndex; i++) {
    if (questionParts.value[i].isGap) {
      gapCount++
    }
  }
  return gapCount
}

// Check if all gaps are filled
const allGapsFilled = computed(() => {
  if (!userAnswers.value || userAnswers.value.length === 0) {
    return false
  }
  return gapPositions.value.every((_, index) => {
    const answer = userAnswers.value[index]
    return answer && answer.trim().length > 0
  })
})

// Track which gaps are empty for highlighting
const emptyGaps = computed(() => {
  if (!validationError.value)
    return new Set()

  const empty = new Set<number>()
  gapPositions.value.forEach((_, index) => {
    const answer = userAnswers.value[index]
    if (!answer || answer.trim().length === 0) {
      empty.add(index)
    }
  })
  return empty
})

// Reset component state when assessment changes
watch(
  () => props.assessment,
  () => {
    resetComponentState()
  },
  { immediate: true },
)

function resetComponentState() {
  userAnswers.value = []
  answerSubmitted.value = false
  showFeedback.value = false
  isCorrect.value = false
  loading.value = false
  validationError.value = null
}

function handleAnswerUpdate(gapIndex: number, value: string) {
  if (answerSubmitted.value)
    return

  // Clear any validation error when user starts typing
  if (validationError.value) {
    validationError.value = null
  }

  // Ensure userAnswers array is large enough
  while (userAnswers.value.length <= gapIndex) {
    userAnswers.value.push('')
  }

  userAnswers.value[gapIndex] = value
}

function handleSubmitAnswer() {
  if (answerSubmitted.value)
    return

  answerSubmitted.value = true
  loading.value = true

  // Check if all gaps are filled before submitting
  const allFilled = gapPositions.value.every((_, index) => {
    return (userAnswers.value[index]?.trim().length || 0) > 0
  })

  if (!allFilled) {
    // Show validation error without emitting result
    loading.value = false
    validationError.value = 'Please fill in all the gaps before submitting.'
    answerSubmitted.value = false
    return
  }

  // Simulate loading delay for better UX
  setTimeout(() => {
    // Check answers against correct answers
    const correctCount = userAnswers.value.reduce((count, userAnswer, index) => {
      const correctAnswer = props.assessment.correct_answers[index]
      if (correctAnswer && userAnswer.trim().toLowerCase() === correctAnswer.toLowerCase()) {
        count++
      }
      return count
    }, 0)

    const totalGaps = props.assessment.correct_answers.length
    isCorrect.value = correctCount === totalGaps

    // Calculate score based on percentage correct
    const scorePercentage = totalGaps > 0 ? correctCount / totalGaps : 0
    const score = Math.round(scorePercentage * 10) // Out of 10 points

    emit('answerResult', { isCorrect: isCorrect.value, score })

    loading.value = false
    showFeedback.value = true
  }, 1000)
}

function handleNextQuestion() {
  emit('nextQuestion')
}

// Handle Enter key press to submit
function handleKeyPress(event: KeyboardEvent, gapIndex: number) {
  if (event.key === 'Enter') {
    event.preventDefault()
    if (gapIndex === gapPositions.value.length - 1 && allGapsFilled.value) {
      handleSubmitAnswer()
    }
    else {
      // Focus next input
      const nextInput = document.querySelector(`input[data-gap-index="${gapIndex + 1}"]`) as HTMLInputElement
      if (nextInput) {
        nextInput.focus()
      }
    }
  }
}

// Get feedback message
const feedbackMessage = computed(() => {
  if (!showFeedback.value)
    return ''

  if (props.assessment.explanation) {
    return props.assessment.explanation
  }

  if (isCorrect.value) {
    return 'Great job! You filled in all the gaps correctly.'
  }

  // Show correct answers for incorrect attempts
  const correctAnswersText = props.assessment.correct_answers
    .map((answer, index) => `${index + 1}. ${answer}`)
    .join(', ')
  return `The correct answers are: ${correctAnswersText}`
})
</script>

<template>
  <Card>
    <CardContent>
      <!-- Context with Gap Fill Inputs -->
      <div class="mb-6">
        <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 text-lg leading-relaxed text-center">
          <template v-for="(part, index) in questionParts" :key="index">
            <!-- Regular text -->
            <span v-if="!part.isGap" class="text-gray-800 dark:text-gray-200">
              {{ part.text }}
            </span>

            <!-- Gap input using Input component -->
            <Input
              v-else
              :data-gap-index="getGapIndex(index)"
              :model-value="userAnswers[getGapIndex(index)] || ''"
              :disabled="answerSubmitted"
              :class="{
                'border-green-500 bg-green-50 dark:bg-green-900': showFeedback && isCorrect,
                'border-red-500 bg-red-50 dark:bg-red-900': showFeedback && !isCorrect,
                'border-orange-500 bg-orange-50 dark:bg-orange-900 animate-pulse': validationError && emptyGaps.has(getGapIndex(index)),
                'border-gray-300 dark:border-gray-600': !showFeedback && !validationError,
              }"
              class="inline-block w-24 mx-1 text-center"
              placeholder="___"
              @update:model-value="handleAnswerUpdate(getGapIndex(index), $event as string)"
              @keypress="handleKeyPress($event, getGapIndex(index))"
            />
          </template>
        </div>
      </div>

      <!-- Validation Error Message -->
      <div v-if="validationError" class="mb-4">
        <Alert class="border-orange-500 bg-orange-50 dark:bg-orange-950">
          <div class="flex items-center gap-2">
            <XCircle class="w-5 h-5 text-orange-600 dark:text-orange-400" />
            <AlertTitle class="text-orange-800 dark:text-orange-200">
              Missing Information
            </AlertTitle>
          </div>
          <AlertDescription class="text-orange-700 dark:text-orange-300">
            {{ validationError }}
          </AlertDescription>
        </Alert>
      </div>

      <!-- Instructions -->
      <div v-if="!answerSubmitted" class="mb-6 text-center">
        <!-- Submit Button -->
        <Button
          :disabled="!allGapsFilled || loading"
          size="lg"
          class="px-8"
          @click="handleSubmitAnswer"
        >
          <div v-if="loading" class="flex items-center gap-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
            <span>Checking...</span>
          </div>
          <span v-else>Submit Answer</span>
        </Button>
      </div>

      <!-- Feedback Message -->
      <div v-if="showFeedback" class="mb-6">
        <Alert :class="isCorrect ? 'border-green-500 bg-green-50 dark:bg-green-950' : 'border-red-500 bg-red-50 dark:bg-red-950'">
          <div class="flex items-center gap-2">
            <CheckCircle v-if="isCorrect" class="w-5 h-5 text-green-600 dark:text-green-400" />
            <XCircle v-else class="w-5 h-5 text-red-600 dark:text-red-400" />
            <AlertTitle :class="isCorrect ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'">
              {{ isCorrect ? 'Correct! 🎉' : 'Incorrect 😔' }}
            </AlertTitle>
          </div>
          <AlertDescription :class="isCorrect ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'">
            {{ feedbackMessage }}
          </AlertDescription>
        </Alert>
      </div>

      <!-- Next Button -->
      <div v-if="showFeedback" class="text-center">
        <Button
          size="lg"
          class="px-8 flex items-center gap-2"
          @click="handleNextQuestion"
        >
          <span>{{ isLastQuestion ? 'Complete Level' : 'Next Question' }}</span>
          <Flag v-if="isLastQuestion" class="w-4 h-4" />
          <ChevronRight v-else class="w-4 h-4" />
        </Button>
      </div>
    </CardContent>
  </Card>
</template>
