<script setup lang="ts">
import type { Assessmentable, AssessmentAiGapFillSentence, AssessmentAnswerTheQuestion, AssessmentGapFill, AssessmentMultiSelect } from '@/types/course'
import { Volume2 } from 'lucide-vue-next'
import AnswerTheQuestionGameItem from '~/components/assessments/AnswerTheQuestion/AnswerTheQuestionGameItem.vue'
import AiGapFillGameItem from '~/components/assessments/AssessmentAiGapFillSentence/AiGapFillGameItem.vue'
import GapFillGameItem from '~/components/assessments/GapFill/GapFillGameItem.vue'
import MultiselectGameItem from '~/components/assessments/MultiSelect/MultiselectGameItem.vue'

interface Props {
  assessment: Assessmentable
  unitType: string
  unitId?: number
  isLastQuestion: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'answer-result', result: { isCorrect: boolean, score: number }): void
  (e: 'next-question'): void
}>()

// Audio functionality
const isPlaying = ref(false)
const audioElement = ref<HTMLAudioElement | null>(null)

// Get audio files from the assessment
const audioFiles = computed(() => {
  if (!props.assessment.files)
    return []
  return props.assessment.files.filter(file => file.type === 'audio')
})

// Check if there are any audio files to play
const hasAudio = computed(() => audioFiles.value.length > 0)

// Play audio function
async function playAudio() {
  if (!hasAudio.value)
    return

  try {
    isPlaying.value = true

    // Use the first available audio file
    const audioFile = audioFiles.value[0]

    // Create or reuse audio element
    if (!audioElement.value) {
      audioElement.value = new Audio()

      // Set up event listeners
      audioElement.value.addEventListener('ended', () => {
        isPlaying.value = false
      })

      audioElement.value.addEventListener('error', () => {
        isPlaying.value = false
        console.warn('Failed to play audio file')
      })
    }

    // Set the audio source and play
    audioElement.value.src = audioFile.path
    await audioElement.value.play()
  }
  catch (error) {
    isPlaying.value = false
    console.warn('Audio playback failed:', error)
  }
}

// Auto-play audio when assessment changes
watch(() => props.assessment.assessment_id, async () => {
  if (hasAudio.value) {
    // Small delay to ensure component is fully rendered
    await nextTick()
    setTimeout(() => {
      playAudio()
    }, 500)
  }
}, { immediate: true })

// Cleanup audio element on unmount
onUnmounted(() => {
  if (audioElement.value) {
    audioElement.value.pause()
    audioElement.value = null
  }
})

// Forward events from child components
function handleAnswerResult(result: { isCorrect: boolean, score: number }) {
  emit('answer-result', result)
}

function handleNextQuestion() {
  emit('next-question')
}
</script>

<template>
  <div class="relative">
    <!-- Audio Control -->
    <div v-if="hasAudio" class="absolute top-4 right-4 z-10">
      <Button
        variant="ghost"
        size="sm"
        class="relative bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 hover:bg-white dark:hover:bg-gray-800 shadow-sm"
        :aria-label="isPlaying ? 'Audio playing' : 'Play audio'"
        :title="isPlaying ? 'Audio playing' : 'Play audio'"
        :disabled="isPlaying"
        @click="playAudio"
      >
        <!-- Animated ring while playing -->
        <div
          v-if="isPlaying"
          class="absolute inset-0 rounded-md border-2 border-blue-500 animate-pulse"
        />
        <div
          v-if="isPlaying"
          class="absolute inset-0 rounded-md border border-blue-300 animate-ping"
        />

        <!-- Speaker icon -->
        <Volume2 class="w-4 h-4" :class="{ 'text-blue-600': isPlaying }" />
        <span class="sr-only">{{ isPlaying ? 'Audio playing' : 'Play audio' }}</span>
      </Button>
    </div>

    <!-- Game Item Components -->
    <div class="mb-8">
      <!-- MultiSelect Assessment -->
      <MultiselectGameItem
        v-if="unitType === 'multiple_select'"
        :assessment="assessment as AssessmentMultiSelect"
        :is-last-question="isLastQuestion"
        @answer-result="handleAnswerResult"
        @next-question="handleNextQuestion"
      />

      <!-- AI Gap Fill Assessment -->
      <AiGapFillGameItem
        v-else-if="unitType === 'ai_gap_fill_sentence'"
        :assessment="assessment as AssessmentAiGapFillSentence"
        :unit-id="unitId"
        :is-last-question="isLastQuestion"
        @answer-result="handleAnswerResult"
        @next-question="handleNextQuestion"
      />

      <!-- Gap Fill Assessment -->
      <GapFillGameItem
        v-else-if="unitType === 'gap_fill'"
        :assessment="assessment as AssessmentGapFill"
        :is-last-question="isLastQuestion"
        @answer-result="handleAnswerResult"
        @next-question="handleNextQuestion"
      />

      <!-- Answer the Question Assessment -->
      <AnswerTheQuestionGameItem
        v-else-if="unitType === 'answer_the_question'"
        :assessment="assessment as AssessmentAnswerTheQuestion"
        :is-last-question="isLastQuestion"
        @answer-result="handleAnswerResult"
        @next-question="handleNextQuestion"
      />
    </div>
  </div>
</template>

<style scoped>
/* Enhanced ring animations */
@keyframes subtle-pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes ring-ping {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  75%,
  100% {
    opacity: 0;
    transform: scale(1.1);
  }
}

.animate-pulse {
  animation: subtle-pulse 1.5s ease-in-out infinite;
}

.animate-ping {
  animation: ring-ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
</style>
